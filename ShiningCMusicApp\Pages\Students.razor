@page "/students"
@using ShiningCMusicCommon.Models
@using ShiningCMusicApp.Services
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.DropDowns
@inject IStudentApiService StudentApi
@inject ITutorApiService TutorApi
@inject ISubjectApiService SubjectApi
@inject ILessonApiService LessonApi
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Administrator")]

<PageTitle>Student Management</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-3 mb-md-4 fs-3 fs-md-1">👨‍🎓 <span class="d-none d-sm-inline">Student Management</span><span class="d-sm-none">Students</span></h1>
            
            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading students...</p>
                </div>
            }
            else
            {
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                            <h5 class="mb-2 mb-md-0">Students</h5>
                            <div class="d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end">
                                <button class="btn btn-primary" style="min-width: 150px;" @onclick="OpenCreateModal">
                                    <i class="fas fa-plus"></i>
                                    <span class="d-none d-sm-inline ms-1">Add New Student</span>
                                    <span class="d-sm-none ms-1">Add</span>
                                </button>
                                <button class="btn btn-secondary" style="min-width: 150px;" @onclick="RefreshData">
                                    <i class="fas fa-refresh"></i>
                                    <span class="d-none d-sm-inline ms-1">Refresh</span>
                                    <span class="d-sm-none ms-1">Refresh</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-1 p-md-3">
                        <SfGrid DataSource="@students" AllowPaging="true" AllowSorting="true" AllowFiltering="true"
                                AllowResizing="true" Height="600" CssClass="mobile-grid">
                            <GridPageSettings PageSize="10"></GridPageSettings>
                            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.FilterBar"></GridFilterSettings>
                            <GridColumns>
                                <GridColumn Field=@nameof(Student.StudentId) HeaderText="ID" Width="50" IsPrimaryKey="true" 
                                           AllowFiltering="false"></GridColumn>
                                <GridColumn Field=@nameof(Student.StudentName) HeaderText="Name" Width="150"></GridColumn>
                                <GridColumn Field=@nameof(Student.Email) HeaderText="Email" Width="250"></GridColumn>
                                <GridColumn Field=@nameof(Student.TutorID) HeaderText="Tutor" Width="150">
                                    <Template>
                                        @{
                                            var student = (context as Student);
                                            var tutor = tutors.FirstOrDefault(t => t.TutorId == student?.TutorID);
                                        }
                                        <span>@(tutor?.TutorName ?? "No Tutor")</span>
                                    </Template>
                                </GridColumn>
                                <GridColumn Field=@nameof(Student.SubjectId) HeaderText="Subject" Width="150">
                                    <Template>
                                        @{
                                            var student = (context as Student);
                                            var subject = subjects.FirstOrDefault(s => s.SubjectId == student?.SubjectId);
                                        }
                                        <span>@(subject?.SubjectName ?? "No Subject")</span>
                                    </Template>
                                </GridColumn>
                                <GridColumn Field=@nameof(Student.LoginName) HeaderText="Login Name" Width="100"></GridColumn>
                                <GridColumn Field=@nameof(Student.CreatedUTC) HeaderText="Created" Width="150" Format="d" 
                                           AllowFiltering="false"></GridColumn>
                                <GridColumn HeaderText="Actions" Width="150" AllowFiltering="false" AllowSorting="false">
                                    <Template>
                                        @{
                                            var student = (context as Student);
                                        }
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-outline-primary" style="width: 50%;" @onclick="() => OpenEditModal(student)" 
                                                    title="Edit">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <button class="btn btn-outline-danger" style="width: 50%;" @onclick="() => DeleteStudent(student)"
                                                    title="Delete">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Create/Edit Modal -->
<SfDialog @bind-Visible="showModal" Header="@modalTitle" Width="500px" Height="auto" IsModal="true" 
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentStudent" OnValidSubmit="@SaveStudent">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />
                
                <div class="mb-3">
                    <label class="form-label">Student Name <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="currentStudent.StudentName" Placeholder="Enter student name" 
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentStudent.StudentName)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Email</label>
                    <SfTextBox @bind-Value="currentStudent.Email" Placeholder="Enter email address" 
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentStudent.Email)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Subject @if (!isEditMode) { <span class="text-danger">*</span> }</label>
                    @if (isEditMode)
                    {
                        <SfTextBox @bind-Value="currentStudentSubjectName" Readonly="true"
                                   CssClass="form-control" Placeholder="No subject assigned"></SfTextBox>
                        <small class="form-text text-muted">Subject cannot be changed when editing. Create a new student to assign a different subject.</small>
                    }
                    else
                    {
                        <SfDropDownList TValue="int?" TItem="Subject" @bind-Value="currentStudent.SubjectId"
                                        DataSource="@subjects" CssClass="form-control" Placeholder="Select a subject">
                            <DropDownListFieldSettings Value="SubjectId" Text="SubjectName"></DropDownListFieldSettings>
                            <DropDownListTemplates TItem="Subject">
                                <ItemTemplate Context="subjectItem">
                                    <span>@((subjectItem as Subject)?.SubjectName)</span>
                                </ItemTemplate>
                            </DropDownListTemplates>
                        </SfDropDownList>
                        <small class="form-text text-muted">Select a subject for this student (required)</small>
                        @if (showSubjectValidation)
                        {
                            <div class="text-danger">Subject is required.</div>
                        }
                    }
                </div>

                <div class="mb-3">
                    <label class="form-label">Assigned Tutor @if (!isEditMode) { <span class="text-danger">*</span> }</label>
                    @if (isEditMode)
                    {
                        <SfTextBox @bind-Value="currentStudentTutorName" Readonly="true"
                                   CssClass="form-control" Placeholder="No tutor assigned"></SfTextBox>
                        <small class="form-text text-muted">Tutor cannot be changed when editing. Create a new student to assign a different tutor.</small>
                    }
                    else
                    {
                        <SfDropDownList TValue="int?" TItem="Tutor" @bind-Value="currentStudent.TutorID"
                                        DataSource="@tutors" CssClass="form-control" Placeholder="Select a tutor">
                            <DropDownListFieldSettings Value="TutorId" Text="TutorName"></DropDownListFieldSettings>
                            <DropDownListTemplates TItem="Tutor">
                                <ItemTemplate Context="tutorItem">
                                    <span>@((tutorItem as Tutor)?.TutorName)</span>
                                </ItemTemplate>
                            </DropDownListTemplates>
                        </SfDropDownList>
                        <small class="form-text text-muted">Select a tutor to assign to this student (required)</small>
                        @if (showTutorValidation)
                        {
                            <div class="text-danger">Tutor is required.</div>
                        }
                    }
                </div>
                <div class="mb-3">
                    <label class="form-label">Login Name</label>
                    <SfTextBox @bind-Value="currentStudent.LoginName" Placeholder="Assigned via user management"
                               CssClass="form-control" Readonly="true"></SfTextBox>
                    <small class="form-text text-muted">Login name is managed through the Admin page user assignment</small>
                </div>
                <div class="d-flex justify-content-end gap-2 mt-4">
                    <SfButton CssClass="btn btn-blue-custom" type="submit" Disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        @(isEditMode ? "Update" : "Create")
                    </SfButton>
                    <SfButton CssClass="btn btn-secondary" @onclick="CloseModal">Cancel</SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    private List<Student> students = new();
    private List<Tutor> tutors = new();
    private List<Subject> subjects = new();
    private bool isLoading = true;
    private bool showModal = false;
    private bool isEditMode = false;
    private bool isSaving = false;
    private string modalTitle = "";
    private Student currentStudent = new();
    private string currentStudentSubjectName = "";
    private string currentStudentTutorName = "";
    private bool showSubjectValidation = false;
    private bool showTutorValidation = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        isLoading = true;
        try
        {
            // Load students, tutors, and subjects
            var studentsTask = StudentApi.GetStudentsAsync();
            var tutorsTask = TutorApi.GetTutorsAsync();
            var subjectsTask = SubjectApi.GetSubjectsAsync();

            await Task.WhenAll(studentsTask, tutorsTask, subjectsTask);

            students = await studentsTask;
            tutors = await tutorsTask;
            subjects = await subjectsTask;

            await JSRuntime.InvokeVoidAsync("console.log", $"Loaded {students.Count} students, {tutors.Count} tutors, and {subjects.Count} subjects");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading data: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", $"Error loading data: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task RefreshData()
    {
        await LoadData();
    }

    private void OpenCreateModal()
    {
        currentStudent = new Student();
        currentStudentSubjectName = "";
        currentStudentTutorName = "";
        showSubjectValidation = false;
        showTutorValidation = false;
        isEditMode = false;
        modalTitle = "Create New Student";
        showModal = true;
    }

    private void OpenEditModal(Student? student)
    {
        if (student != null)
        {
            currentStudent = new Student
            {
                StudentId = student.StudentId,
                StudentName = student.StudentName,
                Email = student.Email,
                TutorID = student.TutorID,
                SubjectId = student.SubjectId
            };

            // Set the subject name for readonly display
            var subject = subjects.FirstOrDefault(s => s.SubjectId == student.SubjectId);
            currentStudentSubjectName = subject?.SubjectName ?? "No subject assigned";

            // Set the tutor name for readonly display
            var tutor = tutors.FirstOrDefault(t => t.TutorId == student.TutorID);
            currentStudentTutorName = tutor?.TutorName ?? "No tutor assigned";

            isEditMode = true;
            modalTitle = "Edit Student";
            showModal = true;
        }
    }

    private void CloseModal()
    {
        showModal = false;
        currentStudent = new();
        currentStudentSubjectName = "";
        currentStudentTutorName = "";
        showSubjectValidation = false;
        showTutorValidation = false;
        isSaving = false;
    }

    private async Task SaveStudent()
    {
        // Reset validation flags
        showSubjectValidation = false;
        showTutorValidation = false;

        if (string.IsNullOrWhiteSpace(currentStudent.StudentName))
        {
            await JSRuntime.InvokeVoidAsync("alert", "Student name is required.");
            return;
        }

        // Validate subject and tutor for new students only
        if (!isEditMode)
        {
            bool hasValidationErrors = false;

            if (!currentStudent.SubjectId.HasValue || currentStudent.SubjectId <= 0)
            {
                showSubjectValidation = true;
                hasValidationErrors = true;
            }

            if (!currentStudent.TutorID.HasValue || currentStudent.TutorID <= 0)
            {
                showTutorValidation = true;
                hasValidationErrors = true;
            }

            if (hasValidationErrors)
            {
                StateHasChanged();
                return;
            }
        }

        isSaving = true;
        try
        {
            bool success;
            if (isEditMode)
            {
                success = await StudentApi.UpdateStudentAsync(currentStudent.StudentId, currentStudent);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Student updated successfully!");
                }
            }
            else
            {
                var createdStudent = await StudentApi.CreateStudentAsync(currentStudent);
                success = createdStudent != null;
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Student created successfully!");
                }
            }

            if (success)
            {
                CloseModal();
                await LoadData();
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "Failed to save student. Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error saving student: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", $"Error saving student: {ex.Message}");
        }
        finally
        {
            isSaving = false;
        }
    }

    private async Task DeleteStudent(Student? student)
    {
        if (student == null) return;

        try
        {
            // Check if student has future lessons
            var lessons = await LessonApi.GetLessonsAsync();
            var futureLessons = lessons.Where(l => l.StudentId == student.StudentId && l.StartTime > DateTime.Now).ToList();

            if (futureLessons.Any())
            {
                await JSRuntime.InvokeVoidAsync("alert",
                    $"Cannot delete student '{student.StudentName}' because they have {futureLessons.Count} upcoming lesson(s). Please cancel or reschedule their future lessons first.");
                return;
            }

            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm",
                $"Are you sure you want to delete student '{student.StudentName}'? This action cannot be undone.");

            if (confirmed)
            {
                var success = await StudentApi.DeleteStudentAsync(student.StudentId);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Student deleted successfully!");
                    await LoadData();
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Failed to delete student. Please try again.");
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting student: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", $"Error deleting student: {ex.Message}");
        }
    }
}
